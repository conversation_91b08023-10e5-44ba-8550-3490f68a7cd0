from abc import ABC, abstractmethod
from pathlib import Path
import polars as pl
import pandas as pd
from prefect import flow, task

from base.config import DatasetConfig
from enums.constant import Date


class DatasetPl(ABC):
    @property
    def data(self) -> pl.LazyFrame:
        return self._data

    @data.setter
    def data(self, data: pl.LazyFrame):
        if self.config.data_type == "kline":
            self._data = data.filter(
                pl.col("Open time").is_between(
                    pl.lit(pd.to_datetime(self.config.start_date)),  # type: ignore
                    pl.lit(pd.to_datetime(self.config.end_date)),  # type: ignore
                )
            )
        else:
            raise ValueError("Invalid data type")

        if self.config.symbol is not None:
            self._data = self._data.filter(pl.col("symbol").is_in(self.config.symbol))

    @property
    def config(self) -> DatasetConfig:
        return self._config

    @config.setter
    def config(self, config: DatasetConfig):
        if config.start_date is None:
            config.start_date = Date.START_DATE
        if config.end_date is None:
            config.end_date = Date.END_DATE

        self._config = config

    @task
    def read(self) -> pl.LazyFrame:
        if not Path(self.config.pqt_file_path).exists():
            raise FileNotFoundError(f"{self.config.pqt_file_path} does not exist")

        self.data = pl.scan_parquet(self.config.pqt_file_path).drop(["year", "month"])
        return self.data

    @task
    def save(self):
        self.data.collect().write_parquet(
            self.config.pqt_file_path, partition_by=["symbol", "year", "month"]
        )

    @flow(log_prints=True)
    def get(self, force=False) -> pl.LazyFrame:
        if force:
            self.from_csv()
            return self.data

        try:
            self.read()
        except FileNotFoundError:
            print(f"{self.__class__.__name__} parquet file not found, collect from csv")
            self.from_csv()
        return self.data

    @abstractmethod
    def from_csv(self) -> pl.LazyFrame: ...

    @abstractmethod
    def trans_kunquant(self) -> tuple[dict, list[str], pl.Series]: ...
