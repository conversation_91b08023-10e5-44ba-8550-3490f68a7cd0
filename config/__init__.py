from base.config import DatasetConfig, FactorConfig
from dataset.spot import SpotKlineDataset


def spot_kline_config(start_date: str | None = None, end_date: str | None = None):
    return DatasetConfig(
        data_type="kline",
        csv_dir_path="/home/<USER>/projects/crypto_quant/downloads/spot/monthly/klines",
        pqt_file_path="/home/<USER>/projects/crypto_quant/data/spot/klines.parquet",
        start_date=start_date,
        end_date=end_date,
    )


def alpha101_config(start_date: str | None = None, end_date: str | None = None):
    return FactorConfig(
        file_path="/home/<USER>/projects/crypto_quant/data/factor/alpha101.ncdf",
        so_path="/home/<USER>/projects/crypto_quant/compiled_factor/alpha101/alpha101.so",
        dataset=SpotKlineDataset(
            spot_kline_config(start_date=start_date, end_date=end_date)
        ),
        window=128,
        start_date=start_date,
        end_date=end_date,
    )


def alpha158_config(start_date: str | None = None, end_date: str | None = None):
    return FactorConfig(
        file_path="/home/<USER>/projects/crypto_quant/data/factor/alpha158.ncdf",
        so_path="/home/<USER>/projects/crypto_quant/compiled_factor/alpha158/alpha158.so",
        dataset=SpotKlineDataset(
            spot_kline_config(start_date=start_date, end_date=end_date)
        ),
        window=128,
        start_date=start_date,
        end_date=end_date,
    )
