#!/usr/bin/env python3
"""
XGBoost预测模型使用示例

演示如何使用DataPreprocessor和XGBoostPredictor进行时间序列预测
"""

import sys
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

import polars as pl
import xarray as xr
import numpy as np

from dataset.spot import SpotKlineDataset
from factor.alpha158 import Alpha158SpotKline
from utils.preprocess import DataPreprocessor
from models.xgboost_predictor import XGBoostPredictor
import config


def main():
    print("=" * 60)
    print("XGBoost预测模型使用示例")
    print("=" * 60)
    
    # 设置时间范围
    start_date = "2024-01-01"
    end_date = "2024-03-31"  # 使用较短的时间范围进行演示
    
    print(f"时间范围: {start_date} 到 {end_date}")
    
    # 1. 加载数据
    print("\n1. 加载数据...")
    
    # 加载SpotKlineDataset
    spot_dataset = SpotKlineDataset(config.spot_kline_config(start_date, end_date))
    spot_data = spot_dataset.get()
    
    # 加载Alpha158因子
    alpha158_factor = Alpha158SpotKline(config.alpha158_config(start_date, end_date))
    factor_data = alpha158_factor.get()
    
    print(f"因子数据维度: {factor_data.dims}")
    print(f"因子数量: {len(factor_data.data_vars)}")
    print(f"Symbol数量: {len(factor_data.symbol)}")
    print(f"时间点数量: {len(factor_data.timestamp)}")
    
    # 2. 数据预处理示例
    print("\n2. 数据预处理...")
    
    # 创建数据预处理器
    preprocessor = DataPreprocessor(factor_data)
    
    # 查看原始数据摘要
    print("\n原始数据摘要:")
    preprocessor.summary()
    
    # 计算原始特征统计信息
    original_stats = preprocessor.calculate_feature_stats()
    print(f"\n原始数据中前5个特征的统计信息:")
    for i, (feature, stats) in enumerate(list(original_stats.items())[:5]):
        print(f"  {feature}: 均值={stats['mean']:.4f}, 标准差={stats['std']:.4f}, 缺失率={stats['missing_ratio']:.2%}")
    
    # 数据预处理流水线
    print("\n执行数据预处理流水线...")
    processed_data = (preprocessor
                     .handle_missing_values(method='forward_fill')
                     .remove_outliers(method='iqr', threshold=3.0)
                     .normalize_features(method='standard', per_symbol=True)
                     .get_processed_data())
    
    # 查看处理后数据摘要
    print("\n处理后数据摘要:")
    preprocessor.summary()
    
    # 3. 创建标签
    print("\n3. 创建标签...")
    
    # 计算涨跌标签
    labels = spot_data.with_columns(
        pl.when((pl.col('Close') > pl.col('Close').shift(1)).over('symbol'))
        .then(1)
        .otherwise(0)
        .alias('label')
        .fill_null(0)
    ).select(['symbol', 'Open time', 'label']).rename({'Open time': 'timestamp'}).collect()
    
    print(f"标签数据形状: {labels.shape}")
    print(f"标签分布:")
    label_counts = labels['label'].value_counts().sort('label')
    for row in label_counts.iter_rows():
        print(f"  标签 {row[0]}: {row[1]} 个样本")
    
    # 4. 模型训练示例
    print("\n4. 模型训练...")
    
    # 创建XGBoost预测器
    predictor = XGBoostPredictor(
        use_gpu=True,  # 如果有GPU的话
        n_splits=3,    # 使用3折交叉验证（演示用）
        random_state=42
    )
    
    # 数据对齐
    print("\n对齐特征数据和标签数据...")
    X, y, timestamps, symbols = predictor.align_data(processed_data, labels)
    feature_names = list(processed_data.data_vars)
    
    print(f"对齐后特征矩阵形状: {X.shape}")
    print(f"对齐后标签向量形状: {y.shape}")
    print(f"特征数量: {len(feature_names)}")
    print(f"标签分布: {np.bincount(y.astype(int))}")
    
    # 训练模型
    print("\n开始训练模型...")
    training_results = predictor.train_with_cv(
        X=X,
        y=y,
        timestamps=timestamps,
        feature_names=feature_names
    )
    
    # 5. 结果分析
    print("\n5. 结果分析...")
    
    print("\n交叉验证结果:")
    for metric, scores in training_results['cv_scores'].items():
        mean_score = np.mean(scores)
        std_score = np.std(scores)
        print(f"  {metric}: {mean_score:.4f} ± {std_score:.4f}")
    
    # 显示特征重要性
    if predictor.feature_importance is not None:
        print(f"\n前10个重要特征:")
        for i, row in predictor.feature_importance.head(10).iterrows():
            print(f"  {i+1}. {row['feature']}: {row['importance']:.2f}")
    
    # 6. 预测示例
    print("\n6. 预测示例...")
    
    # 使用部分数据进行预测演示
    test_size = min(1000, len(X))
    X_test = X[:test_size]
    y_test = y[:test_size]
    
    # 预测概率
    y_pred_proba = predictor.predict(X_test, feature_names)
    y_pred = predictor.predict_binary(X_test, threshold=0.5, feature_names=feature_names)
    
    # 计算预测准确率
    accuracy = np.mean(y_pred == y_test)
    print(f"测试集准确率: {accuracy:.4f}")
    print(f"预测概率范围: [{y_pred_proba.min():.4f}, {y_pred_proba.max():.4f}]")
    print(f"预测标签分布: {np.bincount(y_pred)}")
    
    # 7. 模型摘要
    print("\n7. 模型摘要...")
    summary = predictor.get_model_summary()
    print(f"模型参数数量: {len(summary['model_params'])}")
    print(f"交叉验证折数: {summary['cv_folds']}")
    print(f"使用GPU: {summary['use_gpu']}")
    
    # 8. 保存模型（可选）
    print("\n8. 保存模型...")
    model_path = "example_xgboost_model.json"
    predictor.save_model(model_path)
    
    # 测试模型加载
    print("测试模型加载...")
    new_predictor = XGBoostPredictor()
    new_predictor.load_model(model_path)
    
    # 验证加载的模型
    y_pred_loaded = new_predictor.predict(X_test[:10], feature_names)
    print(f"加载模型预测结果与原模型一致: {np.allclose(y_pred_proba[:10], y_pred_loaded)}")
    
    print("\n" + "=" * 60)
    print("示例完成！")
    print("=" * 60)
    
    # 清理临时文件
    import os
    if os.path.exists(model_path):
        os.remove(model_path)
        print(f"已清理临时文件: {model_path}")


if __name__ == "__main__":
    main()
