import torch
from torch import nn
import torch.nn.functional as F
import polars as pl
import xarray as xr

from dataset.spot import SpotKlineDataset
from factor.alpha158 import Alpha158SpotKline
import config

start_date = "2024-01-01"
end_date = "2025-01-01"

ds = SpotKlineDataset(config.spot_kline_config(start_date, end_date))

factor = Alpha158SpotKline(config.alpha158_config(start_date, end_date))

x = factor.get()  # type: ignore

y = ds.get().collect()  # type: ignore

y = y.with_columns(
    pl.when((pl.col('Close') > pl.col('Close').shift(1)).over('symbol'))
    .then(1)
    .otherwise(0)
    .alias('label')
    .fill_null(0)
)
y = y.rename({'Open time': 'timestamp'})

y = y.select(['symbol', 'timestamp', 'label'])

y

xa = y.to_pandas().set_index(['symbol', 'timestamp']).to_xarray()

xa

x

data = xr.merge([x, xa])

data = data.fillna(0)

data = data.to_array()

data.to_numpy().reshape(data.shape[1] * data.shape[2], -1)[:, -1]

data

data.shape

data = data.stack(z=("symbol", "timestamp")).transpose("variable", "z")

data.shape

y = data.sel(variable='label')

y.shape

x = data.drop_sel(variable='label')

x.shape

import numpy as np
# 替换或删除这些值
x = np.nan_to_num(x, posinf=1e10, neginf=-1e10)  # 替换inf
x = np.clip(x, -1e10, 1e10)  # 限制极大值范围

y = y.to_numpy()

x = x.to_numpy()

x.shape

x = x.transpose(-1 , 0)

y.shape

import xgboost as xgb

model = xgb.XGBClassifier(device='cuda')

model.fit(x, y)

pred = model.predict(x)

pred.shape

y.shape

y_true = y
y_pred = pred

from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

# 假设 y_true 是真实值，y_pred 是预测值
accuracy = accuracy_score(y_true, y_pred)
precision = precision_score(y_true, y_pred)
recall = recall_score(y_true, y_pred)
f1 = f1_score(y_true, y_pred)

print(f"准确率(Accuracy): {accuracy:.4f}")
print(f"精确率(Precision): {precision:.4f}")
print(f"召回率(Recall): {recall:.4f}")
print(f"F1分数: {f1:.4f}")

from sklearn.metrics import classification_report

print(classification_report(y_true, y_pred))

