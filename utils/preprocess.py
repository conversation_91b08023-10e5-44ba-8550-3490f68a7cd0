from prefect import flow, task
import polars as pl
import numpy as np


@task(log_prints=True)
def clean_data(df: pl.<PERSON>zy<PERSON>rame) -> pl.LazyFrame:
    # 填充缺失的数据
    all_timestamps = df.select("timestamp").unique()
    all_symbols = df.select("symbol").unique()
    full_combinations = all_timestamps.join(all_symbols, how="cross")  # 生成笛卡尔积

    # 2. 左连接原始数据，缺失的会是 null
    filled_df = full_combinations.join(df, on=["timestamp", "symbol"], how="left")

    # 3. 将 null 填充为 0
    cols = pl.all().exclude(
        [pl.Datetime, pl.Date, pl.Object, pl.String, pl.<PERSON>, pl.Struct]
    )

    # 执行填充操作
    df = filled_df.with_columns(cols.fill_null(0))
    return df


@task(log_prints=True)
def fillna_mad_zscore_rolling(
    df: pl.<PERSON><PERSON><PERSON>, window_size: int = 20, mad_k: int = 3
) -> pl.LazyFrame:
    cols = pl.all().exclude(
        [pl.Datetime, pl.Date, pl.Object, pl.String, pl.<PERSON>, pl.Struct]
    )

    df = df.with_columns(cols.fill_null(0))
    df = df.with_columns(cols.fill_nan(0))

    # MAD去极值(rolling)
    def rolling_mad_expr(cols: pl.Expr) -> pl.Expr:
        # 计算滚动中位数
        rolling_median = cols.rolling_median(window_size=window_size, min_samples=1)

        # 计算绝对偏差
        abs_dev = (cols - rolling_median).abs()

        # 计算滚动MAD（绝对偏差的滚动中位数）
        rolling_mad = abs_dev.rolling_median(window_size=window_size, min_samples=1)

        # 计算上下限
        lower_bound = rolling_median - mad_k * rolling_mad
        upper_bound = rolling_median + mad_k * rolling_mad

        return (
            pl.when(cols < lower_bound)
            .then(lower_bound)
            .when(cols > upper_bound)
            .then(upper_bound)
            .otherwise(cols)
        )

    df = df.with_columns(rolling_mad_expr(cols).over(pl.col("symbol")))

    # Z值标准化(rolling)
    df = df.with_columns(
        (
            (cols - cols.rolling_mean(window_size, min_samples=1))
            / cols.rolling_std(window_size, min_samples=1)
        ).over(pl.col("symbol"))
    )

    df = df.with_columns(cols.fill_null(0))
    df = df.with_columns(cols.fill_nan(0))
    return df

