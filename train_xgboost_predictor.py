#!/usr/bin/env python3
"""
XGBoost时间序列预测模型训练脚本

使用Alpha158因子和SpotKlineDataset标签进行训练
包含数据预处理、模型训练、交叉验证和结果评估
"""

import os
import sys
import argparse
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

import polars as pl
import xarray as xr
import numpy as np
import pandas as pd
from datetime import datetime

from dataset.spot import SpotKlineDataset
from factor.alpha158 import Alpha158SpotKline
from utils.preprocess import DataPreprocessor
from models.xgboost_predictor import XGBoostPredictor
import config


def create_labels(spot_data: pl.LazyFrame) -> pl.DataFrame:
    """
    创建涨跌标签
    
    Args:
        spot_data: SpotKlineDataset数据
        
    Returns:
        包含标签的DataFrame
    """
    print("创建涨跌标签...")
    
    # 计算涨跌标签
    labels = spot_data.with_columns(
        pl.when((pl.col('Close') > pl.col('Close').shift(1)).over('symbol'))
        .then(1)
        .otherwise(0)
        .alias('label')
        .fill_null(0)
    ).select(['symbol', 'Open time', 'label']).rename({'Open time': 'timestamp'})
    
    return labels.collect()


def main():
    parser = argparse.ArgumentParser(description='XGBoost时间序列预测模型训练')
    parser.add_argument('--start_date', type=str, default='2024-01-01', 
                       help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end_date', type=str, default='2024-12-31', 
                       help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--use_gpu', action='store_true', default=True,
                       help='是否使用GPU加速')
    parser.add_argument('--n_splits', type=int, default=5,
                       help='交叉验证折数')
    parser.add_argument('--output_dir', type=str, default='./results',
                       help='结果输出目录')
    parser.add_argument('--preprocess_method', type=str, default='standard',
                       choices=['standard', 'minmax', 'robust'],
                       help='数据预处理方法')
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print("=" * 60)
    print("XGBoost时间序列预测模型训练")
    print("=" * 60)
    print(f"时间范围: {args.start_date} 到 {args.end_date}")
    print(f"使用GPU: {args.use_gpu}")
    print(f"交叉验证折数: {args.n_splits}")
    print(f"预处理方法: {args.preprocess_method}")
    print(f"输出目录: {output_dir}")
    print("=" * 60)
    
    # 1. 加载数据
    print("\n1. 加载数据...")
    
    # 加载SpotKlineDataset
    spot_dataset = SpotKlineDataset(config.spot_kline_config(args.start_date, args.end_date))
    spot_data = spot_dataset.get()
    
    # 加载Alpha158因子
    alpha158_factor = Alpha158SpotKline(config.alpha158_config(args.start_date, args.end_date))
    factor_data = alpha158_factor.get()
    
    print(f"Spot数据形状: {spot_data.collect().shape}")
    print(f"因子数据形状: {factor_data.dims}")
    print(f"因子数量: {len(factor_data.data_vars)}")
    
    # 2. 创建标签
    print("\n2. 创建标签...")
    labels = create_labels(spot_data)
    print(f"标签数据形状: {labels.shape}")
    print(f"标签分布: {labels['label'].value_counts().sort('label')}")
    
    # 3. 数据预处理
    print("\n3. 数据预处理...")
    preprocessor = DataPreprocessor(factor_data)
    
    # 数据预处理流水线
    processed_data = (preprocessor
                     .handle_missing_values(method='forward_fill')
                     .remove_outliers(method='iqr', threshold=3.0)
                     .normalize_features(method=args.preprocess_method, per_symbol=True)
                     .get_processed_data())
    
    # 计算特征统计信息
    feature_stats = preprocessor.calculate_feature_stats()
    
    # 打印数据摘要
    preprocessor.summary()
    
    # 4. 数据对齐和准备
    print("\n4. 数据对齐...")
    predictor = XGBoostPredictor(
        use_gpu=args.use_gpu,
        n_splits=args.n_splits,
        random_state=42
    )
    
    X, y, timestamps, symbols = predictor.align_data(processed_data, labels)
    feature_names = list(processed_data.data_vars)
    
    print(f"对齐后特征矩阵形状: {X.shape}")
    print(f"对齐后标签向量形状: {y.shape}")
    print(f"特征数量: {len(feature_names)}")
    
    # 5. 模型训练
    print("\n5. 开始模型训练...")
    training_results = predictor.train_with_cv(
        X=X,
        y=y,
        timestamps=timestamps,
        feature_names=feature_names
    )
    
    # 6. 保存结果
    print("\n6. 保存结果...")
    
    # 保存模型
    model_path = output_dir / f"xgboost_model_{args.start_date}_{args.end_date}.json"
    predictor.save_model(str(model_path))
    
    # 保存特征重要性
    if predictor.feature_importance is not None:
        importance_path = output_dir / f"feature_importance_{args.start_date}_{args.end_date}.csv"
        predictor.feature_importance.to_csv(importance_path, index=False)
        print(f"特征重要性已保存到: {importance_path}")
        
        # 绘制特征重要性图
        importance_plot_path = output_dir / f"feature_importance_{args.start_date}_{args.end_date}.png"
        predictor.plot_feature_importance(top_n=20, save_path=str(importance_plot_path))
    
    # 保存训练结果
    results_path = output_dir / f"training_results_{args.start_date}_{args.end_date}.json"
    import json
    with open(results_path, 'w', encoding='utf-8') as f:
        # 转换numpy数组为列表以便JSON序列化
        serializable_results = {}
        for key, value in training_results.items():
            if key == 'feature_importance' and value is not None:
                serializable_results[key] = value.to_dict('records')
            elif isinstance(value, dict):
                serializable_results[key] = {
                    k: v.tolist() if isinstance(v, np.ndarray) else v
                    for k, v in value.items()
                }
            else:
                serializable_results[key] = value
        
        json.dump(serializable_results, f, indent=2, ensure_ascii=False)
    print(f"训练结果已保存到: {results_path}")
    
    # 保存模型摘要
    summary = predictor.get_model_summary()
    summary_path = output_dir / f"model_summary_{args.start_date}_{args.end_date}.json"
    with open(summary_path, 'w', encoding='utf-8') as f:
        # 处理numpy数组
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, dict):
                return {k: convert_numpy(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            return obj
        
        summary_serializable = convert_numpy(summary)
        json.dump(summary_serializable, f, indent=2, ensure_ascii=False)
    print(f"模型摘要已保存到: {summary_path}")
    
    # 7. 打印最终结果
    print("\n" + "=" * 60)
    print("训练完成！")
    print("=" * 60)
    print("交叉验证结果:")
    for metric, scores in training_results['cv_scores'].items():
        mean_score = np.mean(scores)
        std_score = np.std(scores)
        print(f"  {metric}: {mean_score:.4f} ± {std_score:.4f}")
    
    if predictor.feature_importance is not None:
        print(f"\n前10个重要特征:")
        for i, row in predictor.feature_importance.head(10).iterrows():
            print(f"  {i+1}. {row['feature']}: {row['importance']:.2f}")
    
    print(f"\n所有结果已保存到: {output_dir}")
    print("=" * 60)


if __name__ == "__main__":
    main()
