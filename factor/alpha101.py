import polars as pl
import pandas as pd
from prefect import task, flow
from prefect.futures import wait
from pathlib import Path
from KunQuant.jit import cfake
from KunQuant.Driver import KunCompilerConfig
from KunQuant.Op import Builder, Input, Output
from KunQuant.Stage import Function
from KunQuant.predefined import Alpha101, Alpha158
import KunQuant.runner.KunRunner as kr
import numpy as np

from base.factor import FactorKunQuant
from base.config import FactorConfig, DatasetConfig
from dataset.spot import SpotKlineDataset


class Alpha101SpotKline(FactorKunQuant):

    def __init__(self, factor_config: FactorConfig):
        self.config: FactorConfig = factor_config

    @task(name="calc_alpha101_spot")
    def calc(self, force=False) -> tuple[dict[str, np.ndarray], list[str], np.ndarray]:
        self.dataset.get(force=force)  # type: ignore
        input_dict, symbols, timestamp = self.config.dataset.trans_kunquant()
        # [stocks//8, time, 8]
        num_time = input_dict["open"].shape[1]  # type: ignore
        so_file = Path(self.config.so_path)
        if not so_file.exists():
            self.make()

        lib = kr.Library.load(str(so_file))
        modu = lib.getModule("alpha101")

        executor = kr.createMultiThreadExecutor(self.config.njobs)
        out: dict = kr.runGraph(executor, modu, input_dict, 0, num_time)
        return out, symbols, timestamp.to_numpy()  # type: ignore

    @task
    def make(self) -> None:
        builder = Builder()
        with builder:
            close = Input("close")
            low = Input("low")
            high = Input("high")
            vopen = Input("open")
            amount = Input("amount")
            vol = Input("volume")
            all_data = Alpha101.AllData(
                low=low, high=high, close=close, open=vopen, amount=amount, volume=vol
            )
            for alpha in Alpha101.all_alpha:
                Output(alpha(all_data), alpha.__name__)
        f = Function(builder.ops)
        cfake.compileit(
            [
                (
                    "alpha101",
                    f,
                    KunCompilerConfig(
                        input_layout="STs",
                        output_layout="TS",
                    ),
                )
            ],
            "alpha101",
            cfake.CppCompilerConfig(),
            tempdir=str(Path(self.config.so_path).parent.parent),
            keep_files=True,
            load=False,
        )
