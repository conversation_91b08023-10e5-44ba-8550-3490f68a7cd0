{"cells": [{"cell_type": "code", "execution_count": 1, "id": "5a677066", "metadata": {}, "outputs": [], "source": ["from dataset.spot import SpotKlineDataset\n", "from config import alpha101_config, alpha158_config, spot_kline_config\n", "from factor.alpha101 import Alpha101SpotKline\n", "from factor.alpha158 import Alpha158SpotKline"]}, {"cell_type": "code", "execution_count": 2, "id": "ede17f51", "metadata": {}, "outputs": [], "source": ["ds = SpotKlineDataset(spot_kline_config())"]}, {"cell_type": "code", "execution_count": 3, "id": "1db3dcaf", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">22:02:15.327 | <span style=\"color: #008080; text-decoration-color: #008080\">INFO</span>    | prefect - Starting temporary server on <span style=\"color: #0000ff; text-decoration-color: #0000ff\">http://127.0.0.1:8824</span>\n", "See <span style=\"color: #0000ff; text-decoration-color: #0000ff\">https://docs.prefect.io/3.0/manage/self-host#self-host-a-prefect-server</span> for more information on running a dedicated Prefect server.\n", "</pre>\n"], "text/plain": ["22:02:15.327 | \u001b[36mINFO\u001b[0m    | prefect - Starting temporary server on \u001b[94mhttp://127.0.0.1:8824\u001b[0m\n", "See \u001b[94mhttps://docs.prefect.io/3.0/manage/self-host#self-host-a-prefect-server\u001b[0m for more information on running a dedicated Prefect server.\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">22:02:19.257 | <span style=\"color: #008080; text-decoration-color: #008080\">INFO</span>    | Flow run<span style=\"color: #800080; text-decoration-color: #800080\"> 'robust-reindeer'</span> - Beginning flow run<span style=\"color: #800080; text-decoration-color: #800080\"> 'robust-reindeer'</span> for flow<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\"> 'get'</span>\n", "</pre>\n"], "text/plain": ["22:02:19.257 | \u001b[36mINFO\u001b[0m    | Flow run\u001b[35m 'robust-reindeer'\u001b[0m - Beginning flow run\u001b[35m 'robust-reindeer'\u001b[0m for flow\u001b[1;35m 'get'\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">22:02:19.324 | <span style=\"color: #008080; text-decoration-color: #008080\">INFO</span>    | Task run 'read-ce9' - Finished in state <span style=\"color: #008000; text-decoration-color: #008000\">Completed</span>()\n", "</pre>\n"], "text/plain": ["22:02:19.324 | \u001b[36mINFO\u001b[0m    | Task run 'read-ce9' - Finished in state \u001b[32mCompleted\u001b[0m()\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">22:02:19.342 | <span style=\"color: #008080; text-decoration-color: #008080\">INFO</span>    | Flow run<span style=\"color: #800080; text-decoration-color: #800080\"> 'robust-reindeer'</span> - Finished in state <span style=\"color: #008000; text-decoration-color: #008000\">Completed</span>()\n", "</pre>\n"], "text/plain": ["22:02:19.342 | \u001b[36mINFO\u001b[0m    | Flow run\u001b[35m 'robust-reindeer'\u001b[0m - Finished in state \u001b[32mCompleted\u001b[0m()\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h4>NAIVE QUERY PLAN</h4><p>run <b>LazyFrame.show_graph()</b> to see the optimized version</p><?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.43.0 (0)\n", " -->\n", "<!-- Title: polars_query Pages: 1 -->\n", "<svg width=\"897pt\" height=\"192pt\"\n", " viewBox=\"0.00 0.00 897.00 192.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 188)\">\n", "<title>polars_query</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-188 893,-188 893,4 -4,4\"/>\n", "<!-- p1 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>p1</title>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"696,-184 193,-184 193,-148 696,-148 696,-184\"/>\n", "<text text-anchor=\"middle\" x=\"444.5\" y=\"-162.3\" font-family=\"Times,serif\" font-size=\"14.00\">FILTER BY col(&quot;Open time&quot;).is_between([1900&#45;01&#45;01 00:00:00, 2100&#45;01&#45;01 00:00:00])</text>\n", "</g>\n", "<!-- p2 -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>p2</title>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"598.5,-112 290.5,-112 290.5,-74 598.5,-74 598.5,-112\"/>\n", "<text text-anchor=\"middle\" x=\"444.5\" y=\"-96.8\" font-family=\"Times,serif\" font-size=\"14.00\">simple π 13/15</text>\n", "<text text-anchor=\"middle\" x=\"444.5\" y=\"-81.8\" font-family=\"Times,serif\" font-size=\"14.00\">[&quot;Open time&quot;, &quot;Open&quot;, &quot;High&quot;, ... 10 other columns]</text>\n", "</g>\n", "<!-- p1&#45;&#45;p2 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>p1&#45;&#45;p2</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M444.5,-147.81C444.5,-136.98 444.5,-123.01 444.5,-112.02\"/>\n", "</g>\n", "<!-- p3 -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>p3</title>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"889,-38 0,-38 0,0 889,0 889,-38\"/>\n", "<text text-anchor=\"middle\" x=\"444.5\" y=\"-22.8\" font-family=\"Times,serif\" font-size=\"14.00\">Parquet SCAN [/home/<USER>/projects/crypto_quant/data/spot/klines.parquet/symbol=BNBUSDT/year=2020/month=1/00000000.parquet, ... 433 other sources]</text>\n", "<text text-anchor=\"middle\" x=\"444.5\" y=\"-7.8\" font-family=\"Times,serif\" font-size=\"14.00\">π 12/15;</text>\n", "</g>\n", "<!-- p2&#45;&#45;p3 -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>p2&#45;&#45;p3</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M444.5,-73.83C444.5,-63 444.5,-49.29 444.5,-38.41\"/>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<LazyFrame at 0x757F27574F20>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["ds.get()"]}, {"cell_type": "code", "execution_count": null, "id": "e854d849", "metadata": {}, "outputs": [], "source": ["alpha101 = Alpha101SpotKline(alpha101_config())"]}, {"cell_type": "code", "execution_count": null, "id": "dd6df420", "metadata": {}, "outputs": [], "source": ["alpha158 = Alpha158SpotKline(alpha158_config())"]}, {"cell_type": "code", "execution_count": null, "id": "41f28922", "metadata": {}, "outputs": [], "source": ["ds = alpha158.get()  # type: ignore"]}, {"cell_type": "code", "execution_count": null, "id": "b68fc81f", "metadata": {}, "outputs": [], "source": ["d"]}, {"cell_type": "code", "execution_count": null, "id": "70ff6a0c", "metadata": {}, "outputs": [], "source": ["ds"]}, {"cell_type": "code", "execution_count": null, "id": "7a0ec5a5", "metadata": {}, "outputs": [], "source": ["df = ds.to_dataframe()"]}, {"cell_type": "code", "execution_count": null, "id": "19e88079", "metadata": {}, "outputs": [], "source": ["import polars as pl"]}, {"cell_type": "code", "execution_count": null, "id": "7d854c81", "metadata": {}, "outputs": [], "source": ["pl.<PERSON><PERSON><PERSON><PERSON>(df)"]}, {"cell_type": "code", "execution_count": null, "id": "37d37fa1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}