from dataclasses import dataclass
from abc import abstractmethod, <PERSON>
from typing import Literal, TYPE_CHECKING
from joblib import cpu_count


if TYPE_CHECKING:
    from .data import DatasetPl


@dataclass
class DatasetConfig:
    data_type: Literal["kline"]
    csv_dir_path: str
    pqt_file_path: str
    start_date: str | None = None
    end_date: str | None = None
    symbol: list[str] | None = None


@dataclass
class FactorConfig:
    file_path: str
    so_path: str
    dataset: "DatasetPl"
    window: int
    start_date: str | None = None
    end_date: str | None = None
    symbol: list[str] | None = None
    njobs: int = cpu_count()
