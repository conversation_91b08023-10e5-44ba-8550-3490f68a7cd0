from prefect import flow, task
import polars as pl
import numpy as np
import xarray as xr
import pandas as pd
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
import warnings

warnings.filterwarnings("ignore")


@task(log_prints=True)
def clean_data(df: pl.LazyFrame) -> pl.LazyFrame:
    # 填充缺失的数据
    all_timestamps = df.select("timestamp").unique()
    all_symbols = df.select("symbol").unique()
    full_combinations = all_timestamps.join(all_symbols, how="cross")  # 生成笛卡尔积

    # 2. 左连接原始数据，缺失的会是 null
    filled_df = full_combinations.join(df, on=["timestamp", "symbol"], how="left")

    # 3. 将 null 填充为 0
    cols = pl.all().exclude(
        [pl.Datetime, pl.Date, pl.Object, pl.String, pl.<PERSON>, pl.Struct]
    )

    # 执行填充操作
    df = filled_df.with_columns(cols.fill_null(0))
    return df


@task(log_prints=True)
def fillna_mad_zscore_rolling(
    df: pl.LazyFrame, window_size: int = 20, mad_k: int = 3
) -> pl.LazyFrame:
    cols = pl.all().exclude(
        [pl.Datetime, pl.Date, pl.Object, pl.String, pl.Boolean, pl.Struct]
    )

    df = df.with_columns(cols.fill_null(0))
    df = df.with_columns(cols.fill_nan(0))

    # MAD去极值(rolling)
    def rolling_mad_expr(cols: pl.Expr) -> pl.Expr:
        # 计算滚动中位数
        rolling_median = cols.rolling_median(window_size=window_size, min_samples=1)

        # 计算绝对偏差
        abs_dev = (cols - rolling_median).abs()

        # 计算滚动MAD（绝对偏差的滚动中位数）
        rolling_mad = abs_dev.rolling_median(window_size=window_size, min_samples=1)

        # 计算上下限
        lower_bound = rolling_median - mad_k * rolling_mad
        upper_bound = rolling_median + mad_k * rolling_mad

        return (
            pl.when(cols < lower_bound)
            .then(lower_bound)
            .when(cols > upper_bound)
            .then(upper_bound)
            .otherwise(cols)
        )

    df = df.with_columns(rolling_mad_expr(cols).over(pl.col("symbol")))

    # Z值标准化(rolling)
    df = df.with_columns(
        (
            (cols - cols.rolling_mean(window_size, min_samples=1))
            / cols.rolling_std(window_size, min_samples=1)
        ).over(pl.col("symbol"))
    )

    df = df.with_columns(cols.fill_null(0))
    df = df.with_columns(cols.fill_nan(0))
    return df


class DataPreprocessor:
    """
    数据预处理类，用于处理xarray Dataset格式的因子数据
    包括缺失值填补、标准化、归一化、特征EDA等功能
    """

    def __init__(self, dataset: xr.Dataset):
        """
        初始化数据预处理器

        Args:
            dataset: xarray Dataset格式的因子数据
        """
        self.dataset = dataset.copy()
        self.original_dataset = dataset.copy()
        self.scaler = None
        self.feature_stats = {}

    def handle_missing_values(
        self, method: str = "forward_fill", fill_value: float = 0.0
    ) -> "DataPreprocessor":
        """
        处理缺失值

        Args:
            method: 填补方法 ('forward_fill', 'backward_fill', 'interpolate', 'constant', 'mean', 'median')
            fill_value: 当method='constant'时使用的填补值

        Returns:
            self: 返回自身以支持链式调用
        """
        print(f"处理缺失值，方法: {method}")

        if method == "forward_fill":
            self.dataset = self.dataset.ffill(dim="timestamp")
        elif method == "backward_fill":
            self.dataset = self.dataset.bfill(dim="timestamp")
        elif method == "interpolate":
            self.dataset = self.dataset.interpolate_na(dim="timestamp", method="linear")
        elif method == "constant":
            self.dataset = self.dataset.fillna(fill_value)
        elif method == "mean":
            # 按symbol计算均值填补
            for var in self.dataset.data_vars:
                mean_vals = self.dataset[var].mean(dim="timestamp", skipna=True)
                self.dataset[var] = self.dataset[var].fillna(mean_vals)
        elif method == "median":
            # 按symbol计算中位数填补
            for var in self.dataset.data_vars:
                median_vals = self.dataset[var].median(dim="timestamp", skipna=True)
                self.dataset[var] = self.dataset[var].fillna(median_vals)
        else:
            raise ValueError(f"不支持的填补方法: {method}")

        # 最后用0填补剩余的NaN
        self.dataset = self.dataset.fillna(0)

        return self

    def remove_outliers(
        self, method: str = "iqr", threshold: float = 3.0
    ) -> "DataPreprocessor":
        """
        去除异常值

        Args:
            method: 异常值检测方法 ('iqr', 'zscore', 'mad')
            threshold: 阈值参数

        Returns:
            self: 返回自身以支持链式调用
        """
        print(f"去除异常值，方法: {method}, 阈值: {threshold}")

        for var in self.dataset.data_vars:
            data = self.dataset[var]

            if method == "iqr":
                # 使用IQR方法
                q1 = data.quantile(0.25, dim="timestamp")
                q3 = data.quantile(0.75, dim="timestamp")
                iqr = q3 - q1
                lower_bound = q1 - threshold * iqr
                upper_bound = q3 + threshold * iqr

                # 将异常值替换为边界值
                data = xr.where(data < lower_bound, lower_bound, data)
                data = xr.where(data > upper_bound, upper_bound, data)

            elif method == "zscore":
                # 使用Z-score方法
                mean_val = data.mean(dim="timestamp")
                std_val = data.std(dim="timestamp")
                z_scores = np.abs((data - mean_val) / std_val)

                # 将异常值替换为均值
                data = xr.where(z_scores > threshold, mean_val, data)

            elif method == "mad":
                # 使用MAD方法
                median_val = data.median(dim="timestamp")
                mad_val = (data - median_val).abs().median(dim="timestamp")
                modified_z_scores = 0.6745 * (data - median_val) / mad_val

                # 将异常值替换为中位数
                data = xr.where(np.abs(modified_z_scores) > threshold, median_val, data)

            else:
                raise ValueError(f"不支持的异常值检测方法: {method}")

            self.dataset[var] = data

        return self

    def normalize_features(
        self, method: str = "standard", per_symbol: bool = True
    ) -> "DataPreprocessor":
        """
        特征标准化/归一化

        Args:
            method: 标准化方法 ('standard', 'minmax', 'robust')
            per_symbol: 是否按symbol分别标准化

        Returns:
            self: 返回自身以支持链式调用
        """
        print(f"特征标准化，方法: {method}, 按symbol: {per_symbol}")

        if method == "standard":
            # Z-score标准化
            if per_symbol:
                for var in self.dataset.data_vars:
                    data = self.dataset[var]
                    mean_val = data.mean(dim="timestamp")
                    std_val = data.std(dim="timestamp")
                    self.dataset[var] = (data - mean_val) / std_val
            else:
                for var in self.dataset.data_vars:
                    data = self.dataset[var]
                    mean_val = data.mean()
                    std_val = data.std()
                    self.dataset[var] = (data - mean_val) / std_val

        elif method == "minmax":
            # Min-Max归一化
            if per_symbol:
                for var in self.dataset.data_vars:
                    data = self.dataset[var]
                    min_val = data.min(dim="timestamp")
                    max_val = data.max(dim="timestamp")
                    self.dataset[var] = (data - min_val) / (max_val - min_val)
            else:
                for var in self.dataset.data_vars:
                    data = self.dataset[var]
                    min_val = data.min()
                    max_val = data.max()
                    self.dataset[var] = (data - min_val) / (max_val - min_val)

        elif method == "robust":
            # 鲁棒标准化（使用中位数和MAD）
            if per_symbol:
                for var in self.dataset.data_vars:
                    data = self.dataset[var]
                    median_val = data.median(dim="timestamp")
                    mad_val = (data - median_val).abs().median(dim="timestamp")
                    self.dataset[var] = (data - median_val) / mad_val
            else:
                for var in self.dataset.data_vars:
                    data = self.dataset[var]
                    median_val = data.median()
                    mad_val = (data - median_val).abs().median()
                    self.dataset[var] = (data - median_val) / mad_val
        else:
            raise ValueError(f"不支持的标准化方法: {method}")

        # 处理可能的无穷大和NaN值
        self.dataset = self.dataset.fillna(0)
        for var in self.dataset.data_vars:
            self.dataset[var] = xr.where(
                np.isinf(self.dataset[var]), 0, self.dataset[var]
            )

        return self

    def calculate_feature_stats(self) -> Dict:
        """
        计算特征统计信息

        Returns:
            特征统计信息字典
        """
        print("计算特征统计信息...")

        stats = {}
        for var in self.dataset.data_vars:
            data = self.dataset[var]
            stats[var] = {
                "mean": float(data.mean().values),
                "std": float(data.std().values),
                "min": float(data.min().values),
                "max": float(data.max().values),
                "median": float(data.median().values),
                "q25": float(data.quantile(0.25).values),
                "q75": float(data.quantile(0.75).values),
                "missing_ratio": float((data.isnull().sum() / data.size).values),
                "zero_ratio": float(((data == 0).sum() / data.size).values),
            }

        self.feature_stats = stats
        return stats
