from abc import abstractmethod, ABC
import polars as pl
import pandas as pd
import xarray as xr
from prefect import task, flow
from prefect.futures import wait
from pathlib import Path
import numpy as np

from base.config import FactorConfig
from enums.constant import Date


class FactorKunQuant(ABC):

    @property
    def data(self) -> xr.Dataset:
        return self._data

    @data.setter
    def data(self, data: xr.Dataset):
        self._data = data.sel(
            timestamp=slice(self.config.start_date, self.config.end_date)
        )
        if self.config.symbol is not None:
            self._data = self._data.sel(symbol=self.config.symbol)

    @property
    def config(self) -> FactorConfig:
        return self._config

    @config.setter
    def config(self, config: FactorConfig):
        if config.start_date is None:
            config.start_date = Date.START_DATE
        if config.end_date is None:
            config.end_date = Date.END_DATE

        start_date = pd.to_datetime(config.start_date)  # type: ignore
        start_date = start_date - pd.DateOffset(days=config.window)
        config.dataset.config.start_date = start_date.strftime("%Y-%m-%d")
        config.dataset.config.end_date = config.end_date

        self._config = config

    @task
    def read(self) -> xr.Dataset:
        if not Path(self.config.file_path).exists():
            raise FileNotFoundError(f"{self.config.file_path} does not exist")

        self.data = xr.open_dataset(self.config.file_path)
        return self.data

    @task
    def save(self) -> None:
        Path(self.config.file_path).parent.mkdir(parents=True, exist_ok=True)
        self.data.to_netcdf(self.config.file_path)

    @flow(log_prints=True)
    def get(self, force=False) -> xr.Dataset:
        if force:
            out, symbols, timestamp = self.calc(force=force)
            self.from_kunquant(out, symbols, timestamp)
            self.save()
            self.read()
            return self.data

        try:
            self.read()
        except FileNotFoundError:
            print(f"{self.__class__.__name__} not found, calculate and save it")
            out, symbols, timestamp = self.calc(force=False)
            self.from_kunquant(out, symbols, timestamp)
            self.save()

        self.read()
        return self.data

    @task
    def from_kunquant(
        self, out_dict: dict[str, np.ndarray], symbols: list[str], timestamp: np.ndarray
    ) -> xr.Dataset:
        ds = xr.Dataset(
            {
                k: (["timestamp", "symbol"], v[:, : len(symbols)])
                for k, v in out_dict.items()
            },
            coords={"timestamp": timestamp, "symbol": symbols},
        )
        self.data = ds
        return self.data

    @abstractmethod
    def calc(self, force) -> tuple[dict, list[str], pl.Series]: ...

    @abstractmethod
    def make(self) -> None: ...
